import requests
import logging
import json
from typing import Dict, Any

logger = logging.getLogger(__name__)

class IntentRecognitionService:
    """
    意图识别服务，使用DeepSeek API进行意图分析
    这是一个独立的服务，不依赖于原有的LLM配置
    """

    def __init__(self):
        # 使用独立的DeepSeek API配置，不影响原有功能
        self.api_url = "https://api.siliconflow.cn/v1/chat/completions"
        self.api_key = "sk-jyvdrpqdleqpvvlrpmcqsktikvgqhbjpfbgntjtbhntaupcp"
        self.model = "deepseek-ai/DeepSeek-V3"
        
        # 意图识别的系统提示词
        self.system_prompt = """你是一个智能意图识别助手。请分析用户的输入，判断用户的意图类型。

意图类型定义：
1. knowledge_query: 知识问答 - 用户询问医学知识、疾病信息、症状解释、治疗方法等
2. tool_use: 工具调用 - 用户需要使用特定功能，如预约挂号、查看报告、药物查询等
3. general_chat: 一般聊天 - 问候、闲聊、情感表达等非医疗相关的对话

请仅返回JSON格式的结果，包含以下字段：
{
    "intent": "knowledge_query|tool_use|general_chat",
    "confidence": 0.0-1.0,
    "reasoning": "判断理由"
}

示例：
用户输入："头痛是什么原因引起的？"
返回：{"intent": "knowledge_query", "confidence": 0.95, "reasoning": "用户询问头痛的原因，属于医学知识问答"}

用户输入："我想预约明天的门诊"
返回：{"intent": "tool_use", "confidence": 0.9, "reasoning": "用户需要使用预约挂号功能"}

用户输入："你好，今天天气不错"
返回：{"intent": "general_chat", "confidence": 0.85, "reasoning": "用户进行日常问候和闲聊"}
"""
    
    def recognize_intent(self, user_message: str) -> Dict[str, Any]:
        """
        识别用户消息的意图

        Args:
            user_message (str): 用户输入的消息

        Returns:
            Dict[str, Any]: 包含意图类型、置信度和推理过程的字典
        """
        logger.info(f"开始意图识别，用户消息: {user_message}")
        try:
            # 构建请求数据
            request_data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": self.system_prompt
                    },
                    {
                        "role": "user",
                        "content": f"请分析以下用户输入的意图：\n\n{user_message}"
                    }
                ],
                "temperature": 0.1,  # 降低随机性，提高一致性
                "max_tokens": 200
            }
            
            # 设置请求头
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 发送请求
            logger.info(f"发送请求到DeepSeek API: {self.api_url}")
            response = requests.post(
                self.api_url,
                headers=headers,
                json=request_data,
                timeout=8  # 减少后端超时时间，确保在前端超时前完成
            )
            logger.info(f"收到响应，状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                logger.info(f"API返回内容: {content}")
                
                # 尝试解析JSON响应
                try:
                    # 清理可能的markdown格式
                    cleaned_content = content.strip()
                    if cleaned_content.startswith('```json'):
                        # 移除markdown代码块标记
                        cleaned_content = cleaned_content[7:]  # 移除 ```json
                        if cleaned_content.endswith('```'):
                            cleaned_content = cleaned_content[:-3]  # 移除结尾的 ```
                    elif cleaned_content.startswith('```'):
                        # 移除普通代码块标记
                        cleaned_content = cleaned_content[3:]
                        if cleaned_content.endswith('```'):
                            cleaned_content = cleaned_content[:-3]

                    cleaned_content = cleaned_content.strip()
                    logger.info(f"清理后的内容: {cleaned_content}")

                    intent_result = json.loads(cleaned_content)
                    
                    # 验证返回的字段
                    if "intent" in intent_result and "confidence" in intent_result:
                        # 确保意图类型有效
                        valid_intents = ["knowledge_query", "tool_use", "general_chat"]
                        if intent_result["intent"] not in valid_intents:
                            logger.warning(f"Invalid intent type: {intent_result['intent']}")
                            intent_result["intent"] = "general_chat"
                        
                        # 确保置信度在有效范围内
                        confidence = float(intent_result["confidence"])
                        if not 0 <= confidence <= 1:
                            confidence = 0.5
                        intent_result["confidence"] = confidence
                        
                        logger.info(f"Intent recognition successful: {intent_result}")
                        return intent_result
                    else:
                        logger.error("Invalid response format from intent recognition API")
                        return self._get_fallback_intent()
                        
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse intent recognition response: {e}")
                    logger.error(f"Raw response: {content}")
                    return self._get_fallback_intent()
            else:
                logger.error(f"Intent recognition API error: {response.status_code} - {response.text}")
                return self._get_fallback_intent()
                
        except requests.exceptions.RequestException as e:
            logger.warning(f"Intent recognition API request failed: {e}")
            return self._get_fallback_intent()
        except Exception as e:
            logger.warning(f"Intent recognition service error: {e}")
            return self._get_fallback_intent()
    
    def _get_fallback_intent(self) -> Dict[str, Any]:
        """
        当意图识别失败时返回的默认意图
        
        Returns:
            Dict[str, Any]: 默认意图结果
        """
        return {
            "intent": "general_chat",
            "confidence": 0.3,
            "reasoning": "意图识别服务不可用，使用默认意图"
        }
    
    def get_intent_description(self, intent: str) -> str:
        """
        获取意图类型的中文描述
        
        Args:
            intent (str): 意图类型
            
        Returns:
            str: 中文描述
        """
        descriptions = {
            "knowledge_query": "知识问答",
            "tool_use": "工具调用", 
            "general_chat": "一般聊天"
        }
        return descriptions.get(intent, "未知意图")
